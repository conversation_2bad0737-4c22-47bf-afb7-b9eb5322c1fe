import { notFound } from 'next/navigation';
import type React from 'react';
import PlasmicProfilePageLayout, {
  PlasmicProfilePageLayout,
} from '@/components/plasmic/autogenerated/senergy_platform/PlasmicProfilePageLayout';
import { ProfileProvider } from '@/contexts/profile-context';
import { getPayloadClient } from '@/lib/payload';

// Generate static params for all published profiles
export async function generateStaticParams() {
  try {
    const payload = await getPayloadClient();

    const profiles = await payload.find({
      collection: 'profile',
      where: {
        _status: {
          equals: 'published',
        },
      },
      limit: 1000, // Use appropriate limit based on expected number of profiles
    });

    return profiles.docs.map((profile) => ({
      slug: profile.slug,
    }));
  } catch (error) {
    throw new Error('Error generating static params for profiles:', {
      cause: error,
    });
  }
}

// Define the params type for Next.js 15 compatibility
type Props = {
  children: React.ReactNode;
  params: Promise<{ slug: string }>;
};

// Main page component
export default async function ProfileLayout({ children, params }: Props) {
  // Await the params promise to get the slug
  const { slug } = await params;

  try {
    const payload = await getPayloadClient();

    const profiles = await payload.find({
      collection: 'profile',
      where: {
        slug: {
          equals: slug,
        },
        _status: {
          equals: 'published',
        },
      },
      depth: 2, // Adjust depth as needed for your relationships
      overrideAccess: true, // This is a public route
      draft: false, // Only get published versions
    });

    const profile = profiles.docs[0];

    // If profile doesn't exist or isn't published, show 404
    if (!profile) {
      return notFound();
    }

    return (
      <div>
        <PlasmicProfilePageLayout
          content={
            <ProfileProvider profile={profile}>{children}</ProfileProvider>
          }
          endTimeInputValue={profile.end_time ?? undefined}
          locationInputValue={profile.location ?? undefined}
          pitchDescriptorInputValue={profile.pitch_descriptor ?? undefined}
          pitchIntroInputValue={profile.pitch_intro ?? undefined}
          pitchRoleInputValue={profile.primary_role ?? undefined}
          preferredNameInputValue={profile.preferred_name ?? undefined}
          startTimeInputValue={profile.start_time ?? undefined}
        />

        {/* Simple debug view of all profile data - can be removed in production */}
        <details className="mt-12 border-gray-200 border-t pt-8">
          <summary className="cursor-pointer text-gray-500 text-sm">
            Debug: Full Profile Data
          </summary>
          <pre className="mt-4 rounded-md bg-gray-100 p-4 text-xs">
            {JSON.stringify(profile, null, 2)}
          </pre>
        </details>
      </div>
    );
  } catch (_error) {
    return notFound();
  }
}
