import type { CollectionConfig } from 'payload';
import { isAdmin } from '@/access/is-admin';
import { isAdminOrSelf } from '@/access/is-admin-or-self';
import { auth } from '@/auth/server';

export const Users: CollectionConfig = {
  slug: 'users',
  admin: {
    //hidden: true,
  },
  access: {
    read: isAdminOrSelf,
    update: () => false,
    delete: () => false,
    create: () => false,
    admin: isAdmin,
  },
  auth: {
    disableLocalStrategy: true,
    removeTokenFromResponses: true,
    tokenExpiration: 0,
    useAPIKey: false,
    strategies: [
      {
        name: 'better-auth',
        authenticate: async ({ headers }) => {
          const session = await auth.api.getSession({ headers });
          if (!session?.user) {
            return { user: null, error: 'Not authenticated' };
          }
          return { user: { ...session.user, collection: 'users' } };
        },
      },
    ],
  },
  endpoints: [
    {
      path: '/me',
      method: 'get',
      handler: async (req) => {
        const session = await auth.api.getSession({ headers: req.headers });
        if (!session) {
          return new Response(
            JSON.stringify({ message: 'Not authenticated' }),
            {
              status: 401,
              headers: { 'Content-Type': 'application/json' },
            }
          );
        }
        return new Response(
          JSON.stringify({
            user: { ...session.user, collection: 'users' },
          }),
          {
            status: 200,
            headers: { 'Content-Type': 'application/json' },
          }
        );
      },
    },
    {
      path: '/logout',
      method: 'post',
      handler: async (req) => {
        await auth.api.signOut({ headers: req.headers });
        return new Response(JSON.stringify({ message: 'Logged out' }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' },
        });
      },
    },
  ],
  fields: [
    {
      name: 'name',
      label: 'Preferred Name',
      type: 'text',
      virtual: true,
    },
    {
      name: 'email',
      label: 'Email',
      type: 'text',
      required: true,
      virtual: true,
    },
    {
      name: 'emailVerified',
      label: 'Email Verified',
      type: 'checkbox',
      defaultValue: false,
      virtual: true,
    },
    {
      name: 'image',
      label: 'Image',
      type: 'text',
      virtual: true,
    },
    {
      name: 'role',
      label: 'Role',
      type: 'select',
      options: ['waitlisted', 'user', 'admin', 'superadmin'],
      defaultValue: 'waitlisted',
      virtual: true,
    },
    {
      name: 'banned',
      label: 'Banned',
      type: 'checkbox',
      defaultValue: false,
      virtual: true,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'banReason',
      label: 'Ban Reason',
      type: 'text',
      virtual: true,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'banExpires',
      label: 'Banned Until',
      type: 'date',
      virtual: true,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'profile',
      label: 'Profile',
      type: 'join',
      collection: 'profile',
      on: 'owner',
      hasMany: false,
    },
  ],
};
